import { DEFAULT_FONT_STYLE } from "GLOBALS";
import { FontStyle, GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { GeneratedPicDie } from "./GeneratedPicDie.ts";

export class PolyDie extends GeneratedPicDie {
  get dieColor(): string {
    return this.getAttribute("color")!;
  }
  override get instanceClassName(): DieClassName {
    return "PolyDie";
  }
  public override get fontStyle(): FontStyle {
    return {
      ...DEFAULT_FONT_STYLE,
      fill: this.dieColor === "white" ? "#454545" : "white",
      stroke: this.dieColor === "white" ? "#454545" : "white",
    };
  }
  override get tile(): GeneratedPicTile {
    const color: string = this.dieColor ? `-${this.dieColor}` : "";
    return {
      ...super.tile,
      imgPath: `${super.tile.imgPath}/poly`,
      fileName: `RPGMeet_poly-empty-d${this.dieSize}${color}.svg`,
      templateName: `RPGMeet_poly-empty-d${this.dieSize}${color}.svg`,
    };
  }
}
safeCustomDefine(PolyDie);
