import { GeneratedPicTile } from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { DieClassName } from "../helpers/diceClassesIndex.ts";
import { PolyDie } from "dice/die/PolyhedricDie.ts";

export class OrdemParanormalDie extends PolyDie {
  override get instanceClassName(): DieClassName {
    return "OrdemParanormalDie";
  }
  override get tile(): GeneratedPicTile {
    return {
      ...super.tile,
      imgPath: `public/assets/img/ordem-paranormal`,
      fileName: `RPGMeet_ordem-paranormal-${this.dieName}.svg`,
      templateName: `RPGMeet_ordem-paranormal-d${this.dieSize}.svg`,
    };
  }
}
safeCustomDefine(OrdemParanormalDie);
